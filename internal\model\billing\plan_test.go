package billing

import (
	"testing"

	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
)

func TestPlan_HasFeature(t *testing.T) {
	plan := &Plan{
		Name:     "Test Plan",
		Features: []string{"premium_content", "advanced_analytics"},
	}

	tests := []struct {
		name     string
		feature  string
		expected bool
	}{
		{
			name:     "Has premium_content feature",
			feature:  "premium_content",
			expected: true,
		},
		{
			name:     "Has advanced_analytics feature",
			feature:  "advanced_analytics",
			expected: true,
		},
		{
			name:     "Does not have unlimited_access feature",
			feature:  "unlimited_access",
			expected: false,
		},
		{
			name:     "Empty feature string",
			feature:  "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := plan.HasFeature(tt.feature)
			if result != tt.expected {
				t.<PERSON><PERSON>("HasFeature(%s) = %v, want %v", tt.feature, result, tt.expected)
			}
		})
	}
}

func TestPlan_SetDefaults(t *testing.T) {
	plan := &Plan{
		Name:  "Test Plan",
		Price: monetary.Amount(1000),
	}

	plan.SetDefaults()

	// Test default values
	if plan.DurationMonths != 12 {
		t.<PERSON>rf("Expected DurationMonths to be 12, got %d", plan.DurationMonths)
	}

	if plan.TrialDays != 7 {
		t.Errorf("Expected TrialDays to be 7, got %d", plan.TrialDays)
	}

	if plan.Currency != "BRL" {
		t.Errorf("Expected Currency to be BRL, got %s", plan.Currency)
	}

	if !plan.AutoRenew {
		t.Errorf("Expected AutoRenew to be true, got %v", plan.AutoRenew)
	}

	if plan.Status != PlanStatusActive {
		t.Errorf("Expected Status to be %s, got %s", PlanStatusActive, plan.Status)
	}

	// Test that Features slice is initialized
	if plan.Features == nil {
		t.Errorf("Expected Features to be initialized, got nil")
	}

	// Verify that Features is an empty slice, not nil
	if len(plan.Features) != 0 {
		t.Errorf("Expected Features to be empty slice, got %v", plan.Features)
	}
}

func TestPlan_Validate(t *testing.T) {
	tests := []struct {
		name    string
		plan    *Plan
		wantErr bool
	}{
		{
			name: "Valid plan",
			plan: &Plan{
				Name:           "Valid Plan",
				Description:    "A valid plan",
				Price:          monetary.Amount(1000),
				Currency:       "BRL",
				DurationMonths: 12,
				TrialDays:      7,
				Status:         PlanStatusActive,
			},
			wantErr: false,
		},
		{
			name: "Empty name",
			plan: &Plan{
				Name:           "",
				Price:          monetary.Amount(1000),
				Currency:       "BRL",
				DurationMonths: 12,
			},
			wantErr: true,
		},
		{
			name: "Name too long",
			plan: &Plan{
				Name:           "This is a very long plan name that exceeds the maximum allowed length of 100 characters and should fail validation",
				Price:          monetary.Amount(1000),
				Currency:       "BRL",
				DurationMonths: 12,
			},
			wantErr: true,
		},
		{
			name: "Negative price",
			plan: &Plan{
				Name:           "Test Plan",
				Price:          monetary.Amount(-100),
				Currency:       "BRL",
				DurationMonths: 12,
			},
			wantErr: true,
		},
		{
			name: "Invalid currency",
			plan: &Plan{
				Name:           "Test Plan",
				Price:          monetary.Amount(1000),
				Currency:       "INVALID",
				DurationMonths: 12,
			},
			wantErr: true,
		},
		{
			name: "Invalid duration",
			plan: &Plan{
				Name:           "Test Plan",
				Price:          monetary.Amount(1000),
				Currency:       "BRL",
				DurationMonths: 0,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.plan.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("Plan.Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPlan_IsActive(t *testing.T) {
	tests := []struct {
		name     string
		status   PlanStatus
		expected bool
	}{
		{
			name:     "Active plan",
			status:   PlanStatusActive,
			expected: true,
		},
		{
			name:     "Inactive plan",
			status:   PlanStatusInactive,
			expected: false,
		},
		{
			name:     "Archived plan",
			status:   PlanStatusArchived,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			plan := &Plan{Status: tt.status}
			result := plan.IsActive()
			if result != tt.expected {
				t.Errorf("IsActive() = %v, want %v", result, tt.expected)
			}
		})
	}
}
