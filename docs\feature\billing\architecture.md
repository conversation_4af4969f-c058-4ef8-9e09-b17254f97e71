# Billing System Architecture

## Overview

The billing system is built using clean architecture principles, ensuring separation of concerns, testability, and maintainability. This document provides a detailed view of the system's architecture, data flow, and integration patterns.

## Architectural Layers

### 1. Model Layer (`internal/model/billing/`)

The model layer defines the core data structures and business rules:

#### Plan Model
- Defines subscription tiers with pricing and features
- Validates plan configuration and constraints
- Manages external provider integration (Hotmart, Apple Pay)

#### Subscription Model
- Links users to plans with lifecycle management
- Handles trial periods, grace periods, and auto-renewal
- Tracks subscription status and billing cycles

#### Payment Model
- Records all payment transactions for auditing
- Supports multiple payment providers
- Implements webhook deduplication

#### Types and Helpers
- Common constants and enumerations
- Validation utilities and helper functions
- Business logic for date calculations and status transitions

### 2. Repository Layer (`internal/repository/billing/`)

The repository layer handles data persistence with MongoDB:

#### Plan Repository
- CRUD operations for plan management
- Queries by external provider IDs
- Status-based filtering for active plans

#### Subscription Repository
- User subscription management
- Active subscription queries with date filtering
- Bulk operations for subscription updates

#### Payment Repository
- Payment transaction logging
- Webhook deduplication through unique indexes
- Audit trail queries for support and compliance

#### Repository Interface
- Unified interface for all billing repositories
- Dependency injection support
- Transaction management for complex operations

### 3. Service Layer (`internal/service/billing/`)

The service layer implements business logic and orchestration:

#### Plan Service
- Plan creation and validation
- External provider ID conflict detection
- Plan lifecycle management

#### Subscription Service
- Subscription creation with trial period setup
- Grace period handling for cancellations
- Multiple subscription management per user

#### Payment Service
- Payment transaction processing
- Webhook event handling
- Provider-specific payment logic

#### Access Control Service
- Feature-based access validation
- Active subscription checking
- User permission aggregation

### 4. Controller Layer (`internal/controller/billing/`)

The controller layer provides REST API endpoints:

#### Plan Endpoints
- Admin-only plan management
- CRUD operations with proper validation
- External provider integration support

#### Subscription Endpoints
- User subscription management
- Self-service subscription operations
- Subscription status and history queries

#### Payment Endpoints
- Payment history and transaction details
- Admin payment management
- Refund and adjustment operations

#### Webhook Endpoints
- Provider webhook processing
- Signature validation and security
- Idempotent payment processing

## Data Flow Architecture

### Subscription Creation Flow

```mermaid
sequenceDiagram
    participant User
    participant Controller
    participant Service
    participant Repository
    participant Database
    participant PaymentProvider

    User->>Controller: POST /subscriptions
    Controller->>Service: CreateSubscription()
    Service->>Repository: ValidatePlan()
    Repository->>Database: Find Plan
    Database-->>Repository: Plan Data
    Repository-->>Service: Validated Plan
    Service->>Repository: CreateSubscription()
    Repository->>Database: Insert Subscription
    Database-->>Repository: Subscription ID
    Repository-->>Service: Created Subscription
    Service-->>Controller: Subscription Response
    Controller-->>User: 201 Created
    
    Note over PaymentProvider: Webhook triggered on payment
    PaymentProvider->>Controller: Webhook Event
    Controller->>Service: ProcessWebhook()
    Service->>Repository: UpdatePaymentStatus()
    Repository->>Database: Update Subscription
```

### Access Control Flow

```mermaid
sequenceDiagram
    participant Client
    participant Middleware
    participant BillingService
    participant Repository
    participant Database

    Client->>Middleware: Request with JWT
    Middleware->>Middleware: Extract User Context
    Middleware->>BillingService: GetUserFeatures()
    BillingService->>Repository: FindActiveSubscriptions()
    Repository->>Database: Query Subscriptions
    Database-->>Repository: Subscription Data
    Repository-->>BillingService: Active Subscriptions
    BillingService->>Repository: GetPlanFeatures()
    Repository->>Database: Query Plans
    Database-->>Repository: Plan Data
    Repository-->>BillingService: Plan Features
    BillingService-->>Middleware: User Features
    Middleware->>Middleware: Validate Required Features
    alt Features Available
        Middleware->>Client: Allow Request
    else Features Missing
        Middleware->>Client: 403 Forbidden
    end
```

## Integration Architecture

### Authentication Integration

The billing system integrates seamlessly with the existing authentication system:

```go
// Middleware chain for protected endpoints
authChain := []echo.MiddlewareFunc{
    middlewares.AuthGuard(),              // JWT validation
    middlewares.UserContextMiddleware(),   // Extract user info
    middlewares.SubscriptionGuard(...),   // Check subscription access
}
```

### RBAC Integration

Billing works alongside the role-based access control system:

- **Admin Role**: Full plan management capabilities
- **User Role**: Self-service subscription management
- **Feature-Based**: Additional access control based on subscription features

### Service Integration

The billing system integrates with other services:

#### Dashboard Service
- Premium features require active subscriptions
- Feature-based access to advanced analytics
- Subscription status affects available functionality

#### Financial Sheet Service
- Subscription costs tracked in financial data
- Premium features for advanced financial tools
- Integration with payment tracking

#### Notification Service
- Subscription lifecycle notifications
- Payment confirmation emails
- Trial expiration reminders

## Database Architecture

### Collection Design

The billing system uses three main collections:

#### billing.plans
```javascript
{
  _id: ObjectId,
  name: String,
  description: String,
  price: NumberLong,  // monetary.Amount
  currency: String,
  features: [String],
  hotmartProductId: String,
  appleProductId: String,
  durationMonths: Number,
  trialDays: Number,
  autoRenew: Boolean,
  status: String,
  createdAt: Date,
  updatedAt: Date
}
```

#### billing.subscriptions
```javascript
{
  _id: ObjectId,
  userId: ObjectId,
  planId: ObjectId,
  status: String,
  startDate: Date,
  endDate: Date,
  trialEndDate: Date,
  autoRenew: Boolean,
  provider: String,
  providerSubscriptionId: String,
  cancelledAt: Date,
  createdAt: Date,
  updatedAt: Date
}
```

#### billing.payments
```javascript
{
  _id: ObjectId,
  subscriptionId: ObjectId,
  userId: ObjectId,
  amount: NumberLong,  // monetary.Amount
  currency: String,
  status: String,
  provider: String,
  providerTransactionId: String,
  webhookData: Object,
  processedAt: Date,
  createdAt: Date
}
```

### Indexing Strategy

Optimized indexes for performance:

```javascript
// Subscription queries
db.billing_subscriptions.createIndex({ "userId": 1, "status": 1 })
db.billing_subscriptions.createIndex({ "endDate": 1 })
db.billing_subscriptions.createIndex({ "provider": 1, "providerSubscriptionId": 1 })

// Payment deduplication
db.billing_payments.createIndex({ 
  "provider": 1, 
  "providerTransactionId": 1 
}, { unique: true })

// Plan queries
db.billing_plans.createIndex({ "status": 1 })
db.billing_plans.createIndex({ "hotmartProductId": 1 }, { sparse: true })
db.billing_plans.createIndex({ "appleProductId": 1 }, { sparse: true })
```

## Security Architecture

### Authentication & Authorization

- **JWT-based Authentication**: All endpoints require valid JWT tokens
- **Role-based Authorization**: Admin-only endpoints for plan management
- **User Isolation**: Users can only access their own subscription data
- **Feature Validation**: Middleware validates feature access on every request

### Webhook Security

- **Signature Validation**: Verify webhook signatures from payment providers
- **Idempotency**: Prevent duplicate payment processing through unique indexes
- **Rate Limiting**: Implement rate limiting for webhook endpoints
- **IP Whitelisting**: Restrict webhook access to known provider IPs

### Data Protection

- **Sensitive Data Handling**: Payment information stored securely
- **Audit Trail**: Complete transaction history for compliance
- **Encryption**: Sensitive fields encrypted at rest
- **PCI Compliance**: Follow payment card industry standards

## Scalability Considerations

### Horizontal Scaling

- **Stateless Services**: All services are stateless and can be horizontally scaled
- **Database Sharding**: MongoDB collections can be sharded by userId
- **Caching Strategy**: Implement Redis caching for frequently accessed data

### Performance Optimization

- **Efficient Queries**: Proper indexing for fast subscription lookups
- **Connection Pooling**: MongoDB connection pooling for optimal performance
- **Lazy Loading**: Load subscription details only when needed
- **Batch Operations**: Bulk operations for subscription updates

### Monitoring & Observability

- **Metrics Collection**: Track key business and technical metrics
- **Distributed Tracing**: Trace requests across service boundaries
- **Health Checks**: Implement comprehensive health check endpoints
- **Alerting**: Set up alerts for critical system events

## Error Handling Architecture

### Error Categories

The system uses structured error handling:

```go
// Service layer errors
errors.New(errors.Service, "subscription not found", errors.NotFound, nil)
errors.New(errors.Service, "plan validation failed", errors.Validation, err)

// Repository layer errors
errors.New(errors.Repository, "database connection failed", errors.Internal, err)
errors.New(errors.Repository, "duplicate payment", errors.Conflict, err)

// Controller layer errors
errors.New(errors.Controller, "invalid request format", errors.Validation, err)
```

### Error Propagation

- **Structured Errors**: Consistent error format across all layers
- **Error Context**: Preserve error context through the call stack
- **User-Friendly Messages**: Convert technical errors to user-friendly messages
- **Logging**: Comprehensive error logging for debugging and monitoring

## Testing Architecture

### Unit Testing

- **Layer Isolation**: Test each layer independently with mocks
- **Business Logic**: Focus on testing business rules and edge cases
- **Error Scenarios**: Test error handling and edge cases

### Integration Testing

- **End-to-End Flows**: Test complete subscription and payment flows
- **Database Integration**: Test repository layer with real database
- **Webhook Processing**: Test webhook handling with mock providers

### Performance Testing

- **Load Testing**: Test system performance under expected load
- **Stress Testing**: Identify system breaking points
- **Benchmark Testing**: Measure and track performance metrics

This architecture ensures the billing system is robust, scalable, and maintainable while providing a solid foundation for future enhancements and integrations.
