# Webhook Integration Guide

## Overview

The billing system supports webhook integration with multiple payment providers to handle real-time payment notifications. This document covers the implementation, security, and testing of webhook endpoints.

## Supported Providers

### Hotmart
- **Event Types**: Purchase complete, subscription renewal, cancellation
- **Authentication**: Signature-based verification
- **Endpoint**: `POST /v2/billing/webhooks/hotmart`

### Apple Pay
- **Event Types**: Initial purchase, renewal, cancellation, refund
- **Authentication**: Shared secret verification
- **Endpoint**: `POST /v2/billing/webhooks/apple`

## Webhook Implementation

### Hotmart Webhook Handler

```go
func (s *service) ProcessHotmartWebhook(ctx context.Context, payload map[string]interface{}) error {
    // Extract event type
    event, ok := payload["event"].(string)
    if !ok {
        return errors.New(errors.Service, "invalid event type", errors.Validation, nil)
    }

    // Extract purchase data
    data, ok := payload["data"].(map[string]interface{})
    if !ok {
        return errors.New(errors.Service, "invalid webhook data", errors.Validation, nil)
    }

    purchase, ok := data["purchase"].(map[string]interface{})
    if !ok {
        return errors.New(errors.Service, "invalid purchase data", errors.Validation, nil)
    }

    // Process based on event type
    switch event {
    case "PURCHASE_COMPLETE":
        return s.handleHotmartPurchase(ctx, purchase)
    case "SUBSCRIPTION_RENEWAL":
        return s.handleHotmartRenewal(ctx, purchase)
    case "SUBSCRIPTION_CANCELLATION":
        return s.handleHotmartCancellation(ctx, purchase)
    default:
        return errors.New(errors.Service, "unsupported event type", errors.Validation, nil)
    }
}
```

### Apple Pay Webhook Handler

```go
func (s *service) ProcessAppleWebhook(ctx context.Context, payload map[string]interface{}) error {
    // Extract notification type
    notificationType, ok := payload["notification_type"].(string)
    if !ok {
        return errors.New(errors.Service, "invalid notification type", errors.Validation, nil)
    }

    // Verify shared secret
    password, ok := payload["password"].(string)
    if !ok || password != s.config.AppleSharedSecret {
        return errors.New(errors.Service, "invalid shared secret", errors.Unauthorized, nil)
    }

    // Extract receipt data
    receiptData, ok := payload["receipt-data"].(string)
    if !ok {
        return errors.New(errors.Service, "invalid receipt data", errors.Validation, nil)
    }

    // Process based on notification type
    switch notificationType {
    case "INITIAL_BUY":
        return s.handleAppleInitialPurchase(ctx, receiptData)
    case "DID_RENEW":
        return s.handleAppleRenewal(ctx, receiptData)
    case "CANCEL":
        return s.handleAppleCancellation(ctx, receiptData)
    default:
        return errors.New(errors.Service, "unsupported notification type", errors.Validation, nil)
    }
}
```

## Webhook Security

### Signature Verification

#### Hotmart Signature Verification
```go
func (s *service) verifyHotmartSignature(payload []byte, signature string) bool {
    // Calculate expected signature
    mac := hmac.New(sha256.New, []byte(s.config.HotmartWebhookSecret))
    mac.Write(payload)
    expectedSignature := hex.EncodeToString(mac.Sum(nil))
    
    // Compare signatures
    return hmac.Equal([]byte(signature), []byte(expectedSignature))
}
```

#### Apple Shared Secret Verification
```go
func (s *service) verifyAppleSharedSecret(payload map[string]interface{}) bool {
    password, ok := payload["password"].(string)
    if !ok {
        return false
    }
    
    return password == s.config.AppleSharedSecret
}
```

### Controller Security Implementation

```go
func (bc *controller) HotmartWebhook() echo.HandlerFunc {
    return func(c echo.Context) error {
        ctx := c.Request().Context()

        // Read raw body for signature verification
        body, err := io.ReadAll(c.Request().Body)
        if err != nil {
            return errors.New(errors.Controller, "failed to read request body", errors.Validation, err)
        }

        // Verify signature
        signature := c.Request().Header.Get("X-Hotmart-Signature")
        if !bc.Service.VerifyHotmartSignature(body, signature) {
            return errors.New(errors.Controller, "invalid webhook signature", errors.Unauthorized, nil)
        }

        // Parse JSON payload
        var payload map[string]interface{}
        if err := json.Unmarshal(body, &payload); err != nil {
            return errors.New(errors.Controller, "invalid JSON payload", errors.Validation, err)
        }

        // Process webhook
        if err := bc.Service.ProcessHotmartWebhook(ctx, payload); err != nil {
            return err
        }

        return c.JSON(http.StatusOK, map[string]string{"status": "processed"})
    }
}
```

## Webhook Event Processing

### Purchase Complete Flow

```mermaid
sequenceDiagram
    participant Hotmart
    participant WebhookEndpoint
    participant BillingService
    participant Repository
    participant Database

    Hotmart->>WebhookEndpoint: PURCHASE_COMPLETE
    WebhookEndpoint->>WebhookEndpoint: Verify Signature
    WebhookEndpoint->>BillingService: ProcessHotmartWebhook()
    BillingService->>Repository: FindSubscriptionByProductId()
    Repository->>Database: Query Subscription
    Database-->>Repository: Subscription Data
    Repository-->>BillingService: Subscription
    BillingService->>Repository: CreatePayment()
    Repository->>Database: Insert Payment
    BillingService->>Repository: UpdateSubscriptionStatus()
    Repository->>Database: Update Subscription
    BillingService-->>WebhookEndpoint: Success
    WebhookEndpoint-->>Hotmart: 200 OK
```

### Subscription Cancellation Flow

```mermaid
sequenceDiagram
    participant Provider
    participant WebhookEndpoint
    participant BillingService
    participant Repository
    participant Database

    Provider->>WebhookEndpoint: CANCELLATION
    WebhookEndpoint->>WebhookEndpoint: Verify Authentication
    WebhookEndpoint->>BillingService: ProcessCancellationWebhook()
    BillingService->>Repository: FindSubscription()
    Repository->>Database: Query Subscription
    Database-->>Repository: Subscription Data
    Repository-->>BillingService: Subscription
    BillingService->>BillingService: Calculate Grace Period
    BillingService->>Repository: UpdateSubscriptionStatus()
    Repository->>Database: Update to "cancelled"
    Note over Database: Subscription remains active until endDate
    BillingService-->>WebhookEndpoint: Success
    WebhookEndpoint-->>Provider: 200 OK
```

## Idempotency and Deduplication

### Payment Deduplication

The system prevents duplicate payment processing using unique indexes:

```go
// Payment model with unique constraint
type Payment struct {
    Provider              PaymentProvider `json:"provider" bson:"provider" validate:"required"`
    ProviderTransactionID string          `json:"providerTransactionId" bson:"providerTransactionId" validate:"required"`
    // ... other fields
}

// MongoDB unique index
db.billing_payments.createIndex({ 
    "provider": 1, 
    "providerTransactionId": 1 
}, { unique: true })
```

### Webhook Retry Handling

```go
func (s *service) ProcessWebhookWithRetry(ctx context.Context, webhookType string, payload map[string]interface{}) error {
    // Check if webhook was already processed
    transactionID := extractTransactionID(payload)
    if s.isWebhookProcessed(ctx, webhookType, transactionID) {
        return nil // Already processed, return success
    }

    // Process webhook
    err := s.processWebhook(ctx, webhookType, payload)
    if err != nil {
        // Log error for retry mechanism
        s.logWebhookError(ctx, webhookType, transactionID, err)
        return err
    }

    // Mark as processed
    s.markWebhookProcessed(ctx, webhookType, transactionID)
    return nil
}
```

## Testing Webhooks

### Unit Tests

```go
func TestHotmartWebhookProcessing(t *testing.T) {
    // Setup
    mockRepo := &MockBillingRepository{}
    service := billing.NewService(mockRepo)

    // Test payload
    payload := map[string]interface{}{
        "event": "PURCHASE_COMPLETE",
        "data": map[string]interface{}{
            "purchase": map[string]interface{}{
                "transaction": "HP123456789",
                "status": "COMPLETE",
                "product": map[string]interface{}{
                    "id": "HOTMART_PROD_123",
                },
                "buyer": map[string]interface{}{
                    "email": "<EMAIL>",
                },
                "price": map[string]interface{}{
                    "value": 29.99,
                    "currency_value": "BRL",
                },
            },
        },
    }

    // Mock expectations
    mockRepo.Subscriptions().EXPECT().
        FindByHotmartProductID(gomock.Any(), "HOTMART_PROD_123").
        Return(&billing.Subscription{
            ObjectID: primitive.NewObjectID(),
            Status:   billing.SubscriptionStatusTrial,
        }, nil)

    mockRepo.Payments().EXPECT().
        Create(gomock.Any(), gomock.Any()).
        Return("payment_id", nil)

    mockRepo.Subscriptions().EXPECT().
        Update(gomock.Any(), gomock.Any(), gomock.Any()).
        Return(nil)

    // Execute
    err := service.ProcessHotmartWebhook(context.Background(), payload)

    // Assert
    assert.NoError(t, err)
}
```

### Integration Tests

```go
func TestWebhookEndToEnd(t *testing.T) {
    // Setup test server
    e := echo.New()
    db := setupTestDB(t)
    defer cleanupTestDB(t, db)

    service := billing.NewService(billing.New(db))
    controller := billing.NewController(service)

    // Register webhook endpoint
    e.POST("/webhooks/hotmart", controller.HotmartWebhook())

    // Create test subscription
    subscription := createTestSubscription(t, db)

    // Create webhook payload
    payload := createHotmartPayload("PURCHASE_COMPLETE", subscription.HotmartProductID)
    body, _ := json.Marshal(payload)

    // Calculate signature
    signature := calculateHotmartSignature(body, "test_secret")

    // Create request
    req := httptest.NewRequest(http.MethodPost, "/webhooks/hotmart", bytes.NewReader(body))
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("X-Hotmart-Signature", signature)
    rec := httptest.NewRecorder()

    // Execute
    e.ServeHTTP(rec, req)

    // Assert
    assert.Equal(t, http.StatusOK, rec.Code)

    // Verify subscription was updated
    updatedSubscription, err := service.FindSubscription(context.Background(), subscription.ObjectID)
    require.NoError(t, err)
    assert.Equal(t, billing.SubscriptionStatusActive, updatedSubscription.Status)
}
```

### Mock Webhook Testing

```go
func TestWebhookWithMockProvider(t *testing.T) {
    // Setup mock HTTP server
    server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        // Simulate webhook from payment provider
        payload := map[string]interface{}{
            "event": "PURCHASE_COMPLETE",
            "data": map[string]interface{}{
                "purchase": map[string]interface{}{
                    "transaction": "MOCK_TRANSACTION_123",
                    "status": "COMPLETE",
                },
            },
        }

        w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(payload)
    }))
    defer server.Close()

    // Test webhook processing
    client := &http.Client{}
    resp, err := client.Get(server.URL)
    require.NoError(t, err)
    defer resp.Body.Close()

    var payload map[string]interface{}
    err = json.NewDecoder(resp.Body).Decode(&payload)
    require.NoError(t, err)

    // Process webhook
    service := billing.NewService(mockRepo)
    err = service.ProcessHotmartWebhook(context.Background(), payload)
    assert.NoError(t, err)
}
```

## Webhook Configuration

### Environment Variables

```bash
# Hotmart Configuration
HOTMART_WEBHOOK_SECRET=your_hotmart_webhook_secret
HOTMART_WEBHOOK_URL=https://yourdomain.com/v2/billing/webhooks/hotmart

# Apple Pay Configuration
APPLE_SHARED_SECRET=your_apple_shared_secret
APPLE_WEBHOOK_URL=https://yourdomain.com/v2/billing/webhooks/apple

# Security Configuration
WEBHOOK_RATE_LIMIT=100  # requests per minute
WEBHOOK_TIMEOUT=30      # seconds
```

### Provider Configuration

#### Hotmart Setup
1. Log into Hotmart dashboard
2. Navigate to Developer Settings
3. Configure webhook URL: `https://yourdomain.com/v2/billing/webhooks/hotmart`
4. Set webhook secret for signature verification
5. Enable required events: PURCHASE_COMPLETE, SUBSCRIPTION_RENEWAL, SUBSCRIPTION_CANCELLATION

#### Apple Pay Setup
1. Log into App Store Connect
2. Navigate to App Information
3. Configure webhook URL: `https://yourdomain.com/v2/billing/webhooks/apple`
4. Set shared secret for verification
5. Enable required notifications: INITIAL_BUY, DID_RENEW, CANCEL

## Monitoring and Debugging

### Webhook Logging

```go
func (s *service) ProcessHotmartWebhook(ctx context.Context, payload map[string]interface{}) error {
    // Log webhook received
    log.Info("hotmart webhook received",
        "event", payload["event"],
        "transactionId", extractTransactionID(payload))

    err := s.processWebhook(ctx, payload)
    if err != nil {
        // Log processing error
        log.Error("hotmart webhook processing failed",
            "event", payload["event"],
            "transactionId", extractTransactionID(payload),
            "error", err)
        return err
    }

    // Log successful processing
    log.Info("hotmart webhook processed successfully",
        "event", payload["event"],
        "transactionId", extractTransactionID(payload))

    return nil
}
```

### Webhook Metrics

Track important webhook metrics:

- **Processing Time**: Time taken to process webhooks
- **Success Rate**: Percentage of successfully processed webhooks
- **Error Rate**: Percentage of failed webhook processing
- **Retry Count**: Number of webhook retries

### Debugging Tools

```go
// Webhook debugging endpoint (admin only)
func (bc *controller) DebugWebhook() echo.HandlerFunc {
    return func(c echo.Context) error {
        // Get webhook history
        webhookID := c.Param("id")
        webhook, err := bc.Service.GetWebhookDetails(c.Request().Context(), webhookID)
        if err != nil {
            return err
        }

        return c.JSON(http.StatusOK, map[string]interface{}{
            "webhook": webhook,
            "processing_log": webhook.ProcessingLog,
            "retry_attempts": webhook.RetryAttempts,
        })
    }
}
```

This comprehensive webhook integration guide ensures reliable payment processing and proper handling of subscription lifecycle events from multiple payment providers.
