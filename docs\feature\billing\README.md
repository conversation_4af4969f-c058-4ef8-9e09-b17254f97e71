# Billing System Documentation

## Overview

The Billing System is a comprehensive subscription management platform that handles user subscriptions, payment processing, and feature-based access control. It provides a complete solution for managing subscription tiers, processing payments through multiple providers (Hotmart and Apple Pay), and controlling access to premium features throughout the application.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Plans Management](#plans-management)
3. [Subscription System](#subscription-system)
4. [Access Control Integration](#access-control-integration)
5. [Technical Implementation](#technical-implementation)
6. [Integration Examples](#integration-examples)
7. [API Reference](#api-reference)
8. [Testing Strategies](#testing-strategies)

## System Architecture

The billing system follows the clean architecture pattern used throughout the codebase:

```
┌─────────────────┐
│   Controller    │ ← REST API endpoints (v2)
├─────────────────┤
│    Service      │ ← Business logic & orchestration
├─────────────────┤
│   Repository    │ ← MongoDB data persistence
├─────────────────┤
│     Model       │ ← Data structures & validation
└─────────────────┘
```

### Core Components

- **Plan**: Defines subscription tiers with pricing, features, and provider IDs
- **Subscription**: Links users to plans with lifecycle management
- **Payment**: Records all payment transactions for auditing
- **Access Control Middleware**: Feature-based access control system

### Integration Points

The billing system integrates with several existing services:

- **Financial Sheet**: Subscription status affects financial features
- **Dashboard**: Premium features require active subscriptions
- **JWT Authentication**: Subscription context added to user tokens
- **RBAC System**: Billing tiers work alongside role-based access

### Data Flow

```mermaid
graph TD
    A[User Subscribes] --> B[Create Subscription]
    B --> C[Payment Provider Webhook]
    C --> D[Update Payment Status]
    D --> E[Activate Subscription]
    E --> F[Grant Feature Access]
    F --> G[Middleware Validates Access]
    G --> H[User Accesses Premium Features]
```

## Plans Management

### Plan Structure

Plans define the subscription tiers available for purchase:

```go
type Plan struct {
    ObjectID primitive.ObjectID `json:"-" bson:"_id,omitempty"`
    ID       string             `json:"id,omitempty" bson:"-"`

    Name        string          `json:"name" bson:"name" validate:"required,min=3,max=100"`
    Description string          `json:"description" bson:"description" validate:"max=500"`
    Price       monetary.Amount `json:"price" bson:"price" validate:"required,min=0"`
    Currency    string          `json:"currency" bson:"currency" validate:"required,len=3"`

    // Features for access control
    Features []string `json:"features" bson:"features"`

    // External provider IDs
    HotmartProductID string `json:"hotmartProductId,omitempty" bson:"hotmartProductId,omitempty"`
    AppleProductID   string `json:"appleProductId,omitempty" bson:"appleProductId,omitempty"`

    // Plan configuration
    DurationMonths int  `json:"durationMonths" bson:"durationMonths" validate:"required,min=1,max=120"`
    TrialDays      int  `json:"trialDays" bson:"trialDays" validate:"min=0,max=30"`
    AutoRenew      bool `json:"autoRenew" bson:"autoRenew"`

    Status PlanStatus `json:"status" bson:"status" validate:"required"`
}
```

### Plan Configuration

Plans support flexible configuration:

- **Duration**: 1-120 months (default: 12 months)
- **Trial Period**: 0-30 days (default: 7 days)
- **Auto-Renewal**: Configurable per plan (default: enabled)
- **Features**: Array of feature strings for access control
- **Multi-Provider**: Support for Hotmart and Apple Pay

### Plan Creation Example

```json
{
  "name": "Premium Plan",
  "description": "Access to all premium features",
  "price": 2999,
  "currency": "BRL",
  "features": [
    "premium_content",
    "advanced_analytics",
    "priority_support"
  ],
  "hotmartProductId": "HOTMART_PROD_123",
  "appleProductId": "com.dinbora.premium",
  "durationMonths": 12,
  "trialDays": 7,
  "autoRenew": true,
  "status": "active"
}
```

### CRUD Operations

Plan management is restricted to admin users:

```go
func (s *service) CreatePlan(ctx context.Context, plan *billing.Plan) (string, error) {
    // Set defaults and validate
    plan.SetDefaults()
    if err := plan.Validate(); err != nil {
        return "", err
    }

    // Check for duplicate external product IDs
    if plan.HotmartProductID != "" {
        existing, err := s.repo.Plans().FindByHotmartProductID(ctx, plan.HotmartProductID)
        if err == nil && existing != nil {
            return "", errors.New(errors.Service, "plan with this Hotmart product ID already exists", errors.Conflict, nil)
        }
    }
```

## Subscription System

### Subscription Lifecycle

Subscriptions follow a well-defined lifecycle:

1. **Creation**: User subscribes to a plan (starts in trial)
2. **Trial Period**: 7-day free trial (configurable per plan)
3. **Active**: Paid subscription with full access
4. **Cancelled**: User cancels but retains access until end date
5. **Expired**: Subscription ends, access revoked

### Subscription Model

```go
type Subscription struct {
    ObjectID primitive.ObjectID `json:"-" bson:"_id,omitempty"`
    ID       string             `json:"id,omitempty" bson:"-"`

    // Core relationships
    UserID primitive.ObjectID `json:"userId" bson:"userId" validate:"required"`
    PlanID primitive.ObjectID `json:"planId" bson:"planId" validate:"required"`

    // Subscription status and lifecycle
    Status SubscriptionStatus `json:"status" bson:"status" validate:"required"`
```

### Subscription States

```go
const (
    SubscriptionStatusTrial     SubscriptionStatus = "trial"
    SubscriptionStatusActive    SubscriptionStatus = "active"
    SubscriptionStatusCancelled SubscriptionStatus = "cancelled"
    SubscriptionStatusExpired   SubscriptionStatus = "expired"
)
```

### Grace Period Handling

When users cancel subscriptions, they retain access until the end date:

- **Immediate Cancellation**: Status changes to "cancelled"
- **Continued Access**: User keeps features until `EndDate`
- **Grace Period**: No immediate access revocation
- **Final Expiration**: Access removed after `EndDate`

### Multiple Subscriptions

Users can have multiple active subscriptions:

- **Union of Benefits**: User gets all features from all active subscriptions
- **Independent Lifecycles**: Each subscription has its own trial/billing cycle
- **Flexible Management**: Users can subscribe to different plans simultaneously

## Access Control Integration

### Middleware Chain

The billing system integrates with the existing authentication middleware:

```go
// Standard middleware chain
authChain := []echo.MiddlewareFunc{
    middlewares.AuthGuard(),           // JWT validation
    middlewares.UserContextMiddleware(), // Extract user info
    middlewares.SubscriptionGuard(...), // Check subscription access
}
```

### Feature-Based Access Control

The system uses a simplified feature-based model:

```go
type SubscriptionGuardConfig struct {
    // RequiredFeatures specifies features that the user must have access to
    RequiredFeatures []string
    // RequireActiveSubscription specifies if an active subscription is required
    RequireActiveSubscription bool
    // AllowTrial specifies if trial subscriptions are allowed
    AllowTrial bool
    // SkipPaths specifies paths that should skip subscription checks
    SkipPaths []string
}
```

### Predefined Middleware Functions

```go
// Require any active subscription
RequireActiveSubscription(billingService)

// Require specific features
RequireFeature([]string{"premium_content"}, billingService)

// Require premium access (multiple features)
RequirePremiumAccess(billingService)
```

### Dashboard Integration Example

The dashboard package demonstrates billing integration:

```go
func (dc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
    dashboardGroup := currentGroup.Group("/dashboard", middlewares.AuthGuard())

    // Basic dashboard access (no subscription required)
    dashboardGroup.GET("/me", dc.FindDashboard())

    // Premium features requiring subscription
    premiumGroup := dashboardGroup.Group("", middlewares.RequirePremiumAccess(billingService))
    premiumGroup.GET("/advanced-analytics", dc.FindAdvancedAnalytics())
}
```

## Technical Implementation

### Database Schema

The billing system uses three main MongoDB collections:

1. **billing.plans**: Plan definitions and configuration
2. **billing.subscriptions**: User subscription records
3. **billing.payments**: Payment transaction audit trail

### Indexing Strategy

```go
func (r *paymentMongoDB) createIndexes() {
    // Unique index for webhook deduplication
    providerTransactionIndex := mongo.IndexModel{
        Keys: bson.D{
            {Key: "provider", Value: 1},
            {Key: "providerTransactionId", Value: 1},
        },
        Options: options.Index().SetUnique(true),
    }
```

### Error Handling

The system follows the project's error handling patterns:

```go
// Service layer error example
if err != nil {
    return "", errors.New(errors.Service, "failed to create subscription", errors.Internal, err)
}

// Repository layer error example
if mongo.IsDuplicateKeyError(err) {
    return "", errors.New(errors.Repository, "payment already exists", errors.Conflict, err)
}
```

### Performance Considerations

- **Efficient Queries**: Proper indexing for subscription lookups
- **Webhook Deduplication**: Unique indexes prevent duplicate processing
- **Feature Caching**: User features cached in request context
- **Minimal Data Transfer**: Only necessary fields in API responses

## Integration Examples

### Protecting Endpoints

```go
// Require active subscription
router.GET("/premium-feature", handler, 
    middlewares.RequireActiveSubscription(billingService))

// Require specific features
router.GET("/advanced-analytics", handler,
    middlewares.RequireFeature([]string{"advanced_analytics"}, billingService))

// Custom feature requirements
router.GET("/custom-endpoint", handler,
    middlewares.SubscriptionGuard(middlewares.SubscriptionGuardConfig{
        RequiredFeatures: []string{"custom_feature", "premium_content"},
        RequireActiveSubscription: true,
        AllowTrial: false, // Exclude trial users
    }, billingService))
```

### Checking Access in Code

```go
// Service layer access check
func (s *service) GetPremiumData(ctx context.Context, userID primitive.ObjectID) (*Data, error) {
    hasAccess, err := s.billingService.HasActiveSubscription(ctx, userID)
    if err != nil {
        return nil, err
    }
    
    if !hasAccess {
        return nil, errors.New(errors.Service, "premium subscription required", errors.Forbidden, nil)
    }
    
    // Continue with premium logic...
}
```

### Subscription Context

Access subscription information in handlers:

```go
func (c *controller) PremiumHandler() echo.HandlerFunc {
    return func(ctx echo.Context) error {
        // Get subscription context added by middleware
        subscriptionCtx := ctx.Get("subscription_context").(*middlewares.SubscriptionContext)
        
        if subscriptionCtx.IsInTrial {
            // Handle trial user logic
        }
        
        // Use subscriptionCtx.Features to check specific capabilities
        return ctx.JSON(http.StatusOK, response)
    }
}

## API Reference

### Plan Endpoints

All plan management endpoints require admin role.

#### Create Plan
```http
POST /v2/billing/plans
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "name": "Premium Plan",
  "description": "Access to all premium features",
  "price": 2999,
  "currency": "BRL",
  "features": ["premium_content", "advanced_analytics"],
  "hotmartProductId": "HOTMART_PROD_123",
  "durationMonths": 12,
  "trialDays": 7,
  "autoRenew": true,
  "status": "active"
}
```

**Response:**
```json
{
  "id": "507f1f77bcf86cd799439011",
  "message": "Plan created successfully"
}
```

#### Get Plans
```http
GET /v2/billing/plans
Authorization: Bearer <admin_token>
```

**Response:**
```json
{
  "plans": [
    {
      "id": "507f1f77bcf86cd799439011",
      "name": "Premium Plan",
      "description": "Access to all premium features",
      "price": 2999,
      "currency": "BRL",
      "features": ["premium_content", "advanced_analytics"],
      "durationMonths": 12,
      "trialDays": 7,
      "autoRenew": true,
      "status": "active",
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### Update Plan
```http
PUT /v2/billing/plans/:id
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "name": "Updated Premium Plan",
  "price": 3499,
  "features": ["premium_content", "advanced_analytics", "priority_support"]
}
```

#### Delete Plan
```http
DELETE /v2/billing/plans/:id
Authorization: Bearer <admin_token>
```

### Subscription Endpoints

#### Create Subscription
```http
POST /v2/billing/subscriptions
Authorization: Bearer <user_token>
Content-Type: application/json

{
  "planId": "507f1f77bcf86cd799439011",
  "provider": "hotmart"
}
```

**Response:**
```json
{
  "id": "507f1f77bcf86cd799439012",
  "userId": "507f1f77bcf86cd799439010",
  "planId": "507f1f77bcf86cd799439011",
  "status": "trial",
  "startDate": "2024-01-15T10:30:00Z",
  "endDate": "2024-01-22T10:30:00Z",
  "autoRenew": true,
  "provider": "hotmart"
}
```

#### Get User Subscriptions
```http
GET /v2/billing/subscriptions/me
Authorization: Bearer <user_token>
```

**Response:**
```json
{
  "subscriptions": [
    {
      "id": "507f1f77bcf86cd799439012",
      "planId": "507f1f77bcf86cd799439011",
      "planName": "Premium Plan",
      "status": "active",
      "startDate": "2024-01-15T10:30:00Z",
      "endDate": "2025-01-15T10:30:00Z",
      "autoRenew": true,
      "isInTrial": false
    }
  ]
}
```

#### Cancel Subscription
```http
DELETE /v2/billing/subscriptions/:id
Authorization: Bearer <user_token>
```

**Response:**
```json
{
  "message": "Subscription cancelled successfully",
  "endDate": "2025-01-15T10:30:00Z",
  "note": "You will retain access until the end date"
}
```

### Access Control Endpoints

#### Get User Access
```http
GET /v2/billing/access/me
Authorization: Bearer <user_token>
```

**Response:**
```json
{
  "hasActiveSubscription": true,
  "features": [
    "premium_content",
    "advanced_analytics",
    "priority_support"
  ]
}
```

### Webhook Endpoints

#### Hotmart Webhook
```http
POST /v2/billing/webhooks/hotmart
Content-Type: application/json

{
  "event": "PURCHASE_COMPLETE",
  "data": {
    "purchase": {
      "transaction": "HP123456789",
      "status": "COMPLETE",
      "product": {
        "id": "HOTMART_PROD_123"
      },
      "buyer": {
        "email": "<EMAIL>"
      },
      "price": {
        "value": 29.99,
        "currency_value": "BRL"
      }
    }
  }
}
```

#### Apple Pay Webhook
```http
POST /v2/billing/webhooks/apple
Content-Type: application/json

{
  "notification_type": "INITIAL_BUY",
  "password": "your_shared_secret",
  "receipt-data": "base64_encoded_receipt"
}
```

## Testing Strategies

### Unit Testing

Test each layer independently:

```go
// Service layer test example
func TestCreateSubscription(t *testing.T) {
    // Setup
    mockRepo := &MockBillingRepository{}
    service := billing.NewService(mockRepo)

    // Test data
    userID := primitive.NewObjectID()
    planID := primitive.NewObjectID()

    // Mock plan validation
    mockRepo.Plans().EXPECT().Find(gomock.Any(), planID).Return(&billing.Plan{
        ObjectID: planID,
        Status: billing.PlanStatusActive,
        DurationMonths: 12,
        TrialDays: 7,
    }, nil)

    // Mock subscription creation
    mockRepo.Subscriptions().EXPECT().Create(gomock.Any(), gomock.Any()).Return("sub_id", nil)

    // Execute
    subscription, err := service.CreateSubscription(ctx, userID, planID, billing.PaymentProviderHotmart)

    // Assert
    assert.NoError(t, err)
    assert.Equal(t, billing.SubscriptionStatusTrial, subscription.Status)
    assert.Equal(t, userID, subscription.UserID)
    assert.Equal(t, planID, subscription.PlanID)
}
```

### Integration Testing

Test the complete flow:

```go
func TestSubscriptionFlow(t *testing.T) {
    // Setup test database
    db := setupTestDB(t)
    defer cleanupTestDB(t, db)

    // Create test plan
    planRepo := billing.NewPlanRepository(db)
    plan := &billing.Plan{
        Name: "Test Plan",
        Price: monetary.Amount(2999),
        Currency: "BRL",
        Features: []string{"premium_content"},
        DurationMonths: 12,
        TrialDays: 7,
        Status: billing.PlanStatusActive,
    }
    planID, err := planRepo.Create(context.Background(), plan)
    require.NoError(t, err)

    // Create subscription
    service := billing.NewService(billing.New(db))
    userID := primitive.NewObjectID()

    subscription, err := service.CreateSubscription(context.Background(), userID, planID, billing.PaymentProviderHotmart)
    require.NoError(t, err)

    // Verify subscription
    assert.Equal(t, billing.SubscriptionStatusTrial, subscription.Status)

    // Test access control
    hasAccess, err := service.HasActiveSubscription(context.Background(), userID)
    require.NoError(t, err)
    assert.True(t, hasAccess)

    // Test features
    features, err := service.GetUserFeatures(context.Background(), userID)
    require.NoError(t, err)
    assert.Contains(t, features, "premium_content")
}
```

### Middleware Testing

Test access control middleware:

```go
func TestSubscriptionGuard(t *testing.T) {
    // Setup
    e := echo.New()
    mockService := &MockBillingService{}

    // Create middleware
    middleware := middlewares.RequireFeature([]string{"premium_content"}, mockService)

    // Setup request with user context
    req := httptest.NewRequest(http.MethodGet, "/premium", nil)
    rec := httptest.NewRecorder()
    c := e.NewContext(req, rec)

    // Mock user context
    userCtx := &middlewares.UserContext{UserID: "user123"}
    c.Set("user_context", userCtx)

    // Mock service responses
    userID, _ := primitive.ObjectIDFromHex("user123")
    mockService.EXPECT().GetUserFeatures(gomock.Any(), userID).Return([]string{"premium_content"}, nil)

    // Test handler
    handler := func(c echo.Context) error {
        return c.String(http.StatusOK, "success")
    }

    // Execute
    err := middleware(handler)(c)

    // Assert
    assert.NoError(t, err)
    assert.Equal(t, http.StatusOK, rec.Code)
}
```

### Webhook Testing

Test webhook processing:

```go
func TestHotmartWebhook(t *testing.T) {
    // Setup
    e := echo.New()
    controller := billing.NewController(mockService)

    // Create webhook payload
    payload := map[string]interface{}{
        "event": "PURCHASE_COMPLETE",
        "data": map[string]interface{}{
            "purchase": map[string]interface{}{
                "transaction": "HP123456789",
                "status": "COMPLETE",
                "product": map[string]interface{}{
                    "id": "HOTMART_PROD_123",
                },
                "buyer": map[string]interface{}{
                    "email": "<EMAIL>",
                },
            },
        },
    }

    // Create request
    body, _ := json.Marshal(payload)
    req := httptest.NewRequest(http.MethodPost, "/webhooks/hotmart", bytes.NewReader(body))
    req.Header.Set("Content-Type", "application/json")
    rec := httptest.NewRecorder()
    c := e.NewContext(req, rec)

    // Mock service expectations
    mockService.EXPECT().ProcessHotmartWebhook(gomock.Any(), gomock.Any()).Return(nil)

    // Execute
    err := controller.HotmartWebhook()(c)

    // Assert
    assert.NoError(t, err)
    assert.Equal(t, http.StatusOK, rec.Code)
}
```

### Performance Testing

Test system performance under load:

```go
func BenchmarkSubscriptionLookup(b *testing.B) {
    // Setup
    db := setupTestDB(b)
    defer cleanupTestDB(b, db)

    service := billing.NewService(billing.New(db))
    userID := primitive.NewObjectID()

    // Create test subscription
    planID := createTestPlan(b, db)
    _, err := service.CreateSubscription(context.Background(), userID, planID, billing.PaymentProviderHotmart)
    require.NoError(b, err)

    // Benchmark
    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _, err := service.HasActiveSubscription(context.Background(), userID)
        if err != nil {
            b.Fatal(err)
        }
    }
}
```

## Security Considerations

### Authentication & Authorization

- **JWT Validation**: All endpoints require valid JWT tokens
- **Role-Based Access**: Plan management restricted to admin users
- **User Isolation**: Users can only access their own subscriptions
- **Feature Validation**: Middleware validates feature access on every request

### Webhook Security

- **Signature Validation**: Verify webhook signatures from payment providers
- **Idempotency**: Prevent duplicate payment processing
- **Rate Limiting**: Implement rate limiting for webhook endpoints
- **IP Whitelisting**: Restrict webhook access to known provider IPs

### Data Protection

- **Sensitive Data**: Payment information stored securely
- **Audit Trail**: Complete transaction history for compliance
- **Encryption**: Sensitive fields encrypted at rest
- **PCI Compliance**: Follow payment card industry standards

## Monitoring & Observability

### Key Metrics

- **Subscription Conversion Rate**: Trial to paid conversion
- **Churn Rate**: Subscription cancellation rate
- **Revenue Metrics**: Monthly recurring revenue (MRR)
- **Feature Usage**: Track premium feature adoption

### Logging

```go
// Service layer logging example
log.Info("subscription created",
    "userId", userID.Hex(),
    "planId", planID.Hex(),
    "provider", provider,
    "status", subscription.Status)

// Error logging
log.Error("failed to process webhook",
    "provider", "hotmart",
    "transactionId", transactionID,
    "error", err)
```

### Alerts

Set up monitoring alerts for:

- **Failed Payments**: Payment processing failures
- **Webhook Failures**: Webhook processing errors
- **High Churn**: Unusual cancellation rates
- **System Errors**: Service availability issues

This comprehensive documentation provides developers with everything needed to understand, implement, and maintain the billing system. The system is designed to be robust, scalable, and secure while maintaining simplicity in its feature-based access control model.
